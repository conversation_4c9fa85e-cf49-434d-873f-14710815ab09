"""
Knowledge Selection Steering for SAE-based model interventions.

This module implements knowledge selection steering using mutual information
to identify SAE features responsible for driving the tension between scheming
and truthfulness, and applies targeted interventions to control whether models prioritize
scheming behavior or being truthful when these goals conflict.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
import torch
import numpy as np
from dataclasses import dataclass
from sklearn.feature_selection import mutual_info_classif
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm
import multiprocessing

from ..core.sae import SAE

logger = logging.getLogger(__name__)


@dataclass
class MutualInformationResult:
    """Result from mutual information calculation."""

    mi_scores: torch.Tensor
    """Mutual information scores for each SAE feature"""

    expectation: torch.Tensor
    """Expected value differences (E_S[Z_i] - E_T[Z_i]) for each feature"""

    scheming_features: torch.Tensor
    """Indices of features positively correlated with scheming behavior"""

    truthful_features: torch.Tensor
    """Indices of features positively correlated with truthful behavior"""

    top_k_features: torch.Tensor
    """Indices of top-k features with highest mutual information"""

    metadata: Dict[str, Any]
    """Additional metadata about the calculation"""


@dataclass
class FunctionalActivations:
    """Functional SAE activations for scheming vs truthfulness steering."""

    z_scheming: torch.Tensor
    """Functional activation vector for scheming steering (z_S)"""

    z_truthful: torch.Tensor
    """Functional activation vector for truthfulness steering (z_T)"""

    scheming_indices: torch.Tensor
    """Indices of features used for scheming steering"""

    truthful_indices: torch.Tensor
    """Indices of features used for truthfulness steering"""

    metadata: Dict[str, Any]
    """Additional metadata about the functional activations"""


@dataclass
class KnowledgeSteeringResult:
    """Result from scheming vs truthfulness steering intervention."""

    original_output: Any
    """Original model output before intervention"""

    modified_output: Any
    """Modified model output after intervention"""

    steering_direction: str
    """Direction of steering: 'scheming' or 'truthful'"""

    steering_strength: float
    """Strength of the applied steering (alpha parameter)"""

    affected_features: List[int]
    """List of SAE features affected by intervention"""

    metadata: Dict[str, Any]
    """Additional metadata about the intervention"""


class KnowledgeSelectionSteering:
    """
    Knowledge Selection Steering using SAE features.

    Implements the approach for identifying SAE activations responsible for driving
    the tension between scheming and truthfulness, and applying targeted
    interventions to control whether models prioritize scheming behavior or
    being truthful when these goals conflict.
    """

    def __init__(
        self,
        sae: SAE,
        device: str = "cuda",
        num_processes: int = 4,
    ):
        """
        Initialize knowledge selection steering.

        Args:
            sae: Trained SAE for feature extraction
            device: Device to run computations on
            num_processes: Number of processes for mutual information calculation
        """
        self.sae = sae
        self.device = device
        self.num_processes = num_processes

        # Move SAE to device
        self.sae = self.sae.to(device)

        # Cached results
        self._mi_result = None
        self._functional_activations = None

        logger.info(f"Initialized KnowledgeSelectionSteering with SAE on {device}")

    def _estimate_mutual_information(
        self, features_labels: Tuple[np.ndarray, List[int]]
    ) -> np.ndarray:
        """
        Estimate mutual information for a subset of features.

        Args:
            features_labels: Tuple of (features, labels)

        Returns:
            Mutual information scores for the feature subset
        """
        features, labels = features_labels
        return mutual_info_classif(features, labels)

    def _get_sae_activations(
        self, hiddens: torch.Tensor, disable_tqdm: bool = True
    ) -> torch.Tensor:
        """
        Get SAE activations for hidden states.

        Args:
            hiddens: Hidden states tensor [batch_size, hidden_dim]
            disable_tqdm: Whether to disable progress bar

        Returns:
            SAE activations [batch_size, sae_dim]
        """
        encodings = []
        for hidden in tqdm(hiddens, disable=disable_tqdm):
            # Use the SAE's encode method to get activations
            if hasattr(self.sae, "pre_acts"):
                encodings.append(self.sae.pre_acts(hidden))
            elif hasattr(self.sae, "encode"):
                encodings.append(self.sae.encode(hidden))
            else:
                raise AttributeError(
                    "SAE must have either 'pre_acts' or 'encode' method"
                )

        return torch.stack(encodings)

    def calculate_mutual_information(
        self,
        scheming_hiddens: torch.Tensor,
        truthful_hiddens: torch.Tensor,
        top_k: Optional[int] = None,
        top_k_proportion: Optional[float] = None,
        minmax_normalization: bool = False,
        equal_label_examples: bool = True,
        seed: int = 42,
    ) -> MutualInformationResult:
        """
        Calculate mutual information between SAE activations and scheming vs truthfulness behaviors.

        This implements the first step of the knowledge selection steering approach:
        calculating I(Z_i; Y) where Z_i is the ith SAE activation and Y = {S, T}
        represents scheming vs truthful behavior.

        Args:
            scheming_hiddens: Hidden states when model exhibits scheming behavior [N_S, hidden_dim]
            truthful_hiddens: Hidden states when model prioritizes truthfulness [N_T, hidden_dim]
            top_k: Number of top features to select (mutually exclusive with top_k_proportion)
            top_k_proportion: Proportion of top features to select (mutually exclusive with top_k)
            minmax_normalization: Whether to apply MinMax normalization to features
            equal_label_examples: Whether to balance the number of examples per label
            seed: Random seed for reproducibility

        Returns:
            MutualInformationResult containing MI scores, expectations, and selected features
        """
        logger.info(
            "Starting mutual information calculation for scheming vs truthfulness steering"
        )

        # Ensure tensors are on the correct device
        scheming_hiddens = scheming_hiddens.to(self.device)
        truthful_hiddens = truthful_hiddens.to(self.device)

        # Balance examples if requested
        if equal_label_examples and len(scheming_hiddens) != len(truthful_hiddens):
            logger.info("Balancing examples between scheming and truthful behaviors")
            num_examples = min(len(scheming_hiddens), len(truthful_hiddens))

            # Use deterministic sampling
            np.random.seed(seed)
            if len(scheming_hiddens) > num_examples:
                indices = np.random.choice(
                    len(scheming_hiddens), num_examples, replace=False
                )
                scheming_hiddens = scheming_hiddens[indices]
            if len(truthful_hiddens) > num_examples:
                indices = np.random.choice(
                    len(truthful_hiddens), num_examples, replace=False
                )
                truthful_hiddens = truthful_hiddens[indices]

        # Get SAE activations
        logger.info("Extracting SAE activations")
        scheming_sae_acts = self._get_sae_activations(
            scheming_hiddens, disable_tqdm=False
        )
        truthful_sae_acts = self._get_sae_activations(
            truthful_hiddens, disable_tqdm=False
        )

        # Combine activations and create labels
        # Label 1 for scheming, Label 0 for truthfulness
        all_acts = torch.cat([scheming_sae_acts, truthful_sae_acts], dim=0)
        labels = [1] * len(scheming_sae_acts) + [0] * len(truthful_sae_acts)

        # Calculate expectations: E_S[Z_i] - E_T[Z_i]
        mean_scheming = scheming_sae_acts.mean(dim=0)  # E_S[Z_i]
        mean_truthful = truthful_sae_acts.mean(dim=0)  # E_T[Z_i]
        expectation = mean_scheming - mean_truthful  # E_S[Z_i] - E_T[Z_i]

        # Prepare features for mutual information calculation
        if minmax_normalization:
            logger.info("Applying MinMax normalization")
            scaler = MinMaxScaler()
            features_scaled = scaler.fit_transform(
                all_acts.float().detach().cpu().numpy()
            )
        else:
            features_scaled = all_acts.float().detach().cpu().numpy()

        # Clean up GPU memory
        del all_acts, scheming_sae_acts, truthful_sae_acts
        torch.cuda.empty_cache()

        # Calculate mutual information in parallel
        logger.info("Calculating mutual information scores")
        indices = np.arange(features_scaled.shape[1])
        # Split into reasonable chunks (at least 1 feature per chunk)
        num_chunks = min(1000, features_scaled.shape[1])
        splits = np.array_split(indices, num_chunks)

        all_args = []
        for split in splits:
            features_subset = features_scaled[:, split]
            all_args.append((features_subset, labels))

        # Use multiprocessing for MI calculation
        # Handle CUDA multiprocessing issue by using CPU data
        if self.num_processes > 1:
            try:
                with multiprocessing.Pool(processes=self.num_processes) as pool:
                    all_mi_scores = []
                    for result in tqdm(
                        pool.imap(self._estimate_mutual_information, all_args),
                        total=len(all_args),
                        desc="Computing MI",
                    ):
                        all_mi_scores.append(result)
            except RuntimeError as e:
                if "CUDA" in str(e):
                    logger.warning(
                        "CUDA multiprocessing failed, falling back to single process"
                    )
                    all_mi_scores = []
                    for args in tqdm(all_args, desc="Computing MI (single process)"):
                        all_mi_scores.append(self._estimate_mutual_information(args))
                else:
                    raise e
        else:
            # Single process computation
            all_mi_scores = []
            for args in tqdm(all_args, desc="Computing MI (single process)"):
                all_mi_scores.append(self._estimate_mutual_information(args))

        # Combine MI scores
        mi_scores = torch.cat([torch.from_numpy(scores) for scores in all_mi_scores])

        # Select top-k features based on mutual information
        if top_k is not None and top_k_proportion is not None:
            raise ValueError("Cannot specify both top_k and top_k_proportion")

        if top_k_proportion is not None:
            top_k = int(len(mi_scores) * top_k_proportion)
        elif top_k is None:
            top_k = len(mi_scores)  # Use all features

        # Get top-k features with highest MI
        top_k_indices = torch.topk(mi_scores, k=min(top_k, len(mi_scores))).indices

        # Separate features by their correlation with behavior types
        # Z_i is positively correlated with scheming if E_S[Z_i] - E_T[Z_i] > 0
        scheming_mask = expectation > 0
        truthful_mask = expectation < 0

        # Get features that are both in top-k and correlated with each behavior type
        # Ensure all tensors are on the same device for indexing
        top_k_indices = top_k_indices.to(self.device)

        scheming_features = top_k_indices[scheming_mask[top_k_indices]]
        truthful_features = top_k_indices[truthful_mask[top_k_indices]]

        logger.info(
            f"Selected {len(scheming_features)} scheming features and "
            f"{len(truthful_features)} truthful features from top-{top_k}"
        )

        # Cache the result
        self._mi_result = MutualInformationResult(
            mi_scores=mi_scores,
            expectation=expectation,
            scheming_features=scheming_features,
            truthful_features=truthful_features,
            top_k_features=top_k_indices,
            metadata={
                "top_k": top_k,
                "top_k_proportion": top_k_proportion,
                "minmax_normalization": minmax_normalization,
                "equal_label_examples": equal_label_examples,
                "num_scheming_examples": len(scheming_hiddens),
                "num_truthful_examples": len(truthful_hiddens),
                "seed": seed,
            },
        )

        return self._mi_result

    def create_functional_activations(
        self,
        mi_result: Optional[MutualInformationResult] = None,
    ) -> FunctionalActivations:
        """
        Create functional SAE activations z_S and z_T for scheming vs truthfulness steering.

        This implements the construction of functional activations:
        - z_S: functional activation for steering toward scheming behavior
        - z_T: functional activation for steering toward truthful behavior

        For each element z_S_i and z_T_i:
        - Set to 0 if Z_i is not in the selected top-k features
        - Otherwise, set based on the expected values from the MI calculation

        Args:
            mi_result: MutualInformationResult from calculate_mutual_information().
                      If None, uses the cached result from the last MI calculation.

        Returns:
            FunctionalActivations containing z_S and z_T vectors
        """
        if mi_result is None:
            if self._mi_result is None:
                raise ValueError(
                    "No mutual information result available. "
                    "Call calculate_mutual_information() first or provide mi_result."
                )
            mi_result = self._mi_result

        logger.info(
            "Creating functional activations for scheming vs truthfulness steering"
        )

        # Get SAE dimension
        sae_dim = len(mi_result.mi_scores)

        # Initialize functional activations as zeros
        z_scheming = torch.zeros(sae_dim, device=self.device)
        z_truthful = torch.zeros(sae_dim, device=self.device)

        # Set values for scheming features
        # For scheming features, we use the positive expectation values
        if len(mi_result.scheming_features) > 0:
            scheming_expectations = mi_result.expectation[mi_result.scheming_features]
            z_scheming[mi_result.scheming_features] = scheming_expectations
            logger.info(f"Set {len(mi_result.scheming_features)} scheming features")

        # Set values for truthful features
        # For truthful features, we use the absolute value of negative expectations
        if len(mi_result.truthful_features) > 0:
            truthful_expectations = torch.abs(
                mi_result.expectation[mi_result.truthful_features]
            )
            z_truthful[mi_result.truthful_features] = truthful_expectations
            logger.info(f"Set {len(mi_result.truthful_features)} truthful features")

        # Cache the result
        self._functional_activations = FunctionalActivations(
            z_scheming=z_scheming,
            z_truthful=z_truthful,
            scheming_indices=mi_result.scheming_features,
            truthful_indices=mi_result.truthful_features,
            metadata={
                "sae_dim": sae_dim,
                "num_scheming_features": len(mi_result.scheming_features),
                "num_truthful_features": len(mi_result.truthful_features),
                "scheming_norm": z_scheming.norm().item(),
                "truthful_norm": z_truthful.norm().item(),
            },
        )

        logger.info(
            f"Created functional activations: "
            f"z_S norm={z_scheming.norm().item():.4f}, "
            f"z_T norm={z_truthful.norm().item():.4f}"
        )

        return self._functional_activations

    def apply_knowledge_steering(
        self,
        hidden_states: torch.Tensor,
        steering_direction: str,
        alpha: float = 1.5,
        functional_activations: Optional[FunctionalActivations] = None,
    ) -> torch.Tensor:
        """
        Apply scheming vs truthfulness steering to hidden states.

        This implements the core steering mechanism from equations (2), (3), and (4):

        1. Determine values to remove (Eq. 2): z^-_i = min{z_i, z^S_i} when steering to truthful
        2. Determine values to add (Eq. 3): z^+_i = max{z^T_i - z_i, 0} when steering to truthful
        3. Apply intervention (Eq. 4): h' = h + α(-g_φ(z^-) + g_φ(z^+))

        Args:
            hidden_states: Input hidden states [batch_size, seq_len, hidden_dim]
            steering_direction: Either 'scheming' or 'truthful'
            alpha: Steering strength hyperparameter (α in the paper)
            functional_activations: FunctionalActivations from create_functional_activations().
                                  If None, uses cached result.

        Returns:
            Modified hidden states after applying scheming vs truthfulness steering
        """
        if functional_activations is None:
            if self._functional_activations is None:
                raise ValueError(
                    "No functional activations available. "
                    "Call create_functional_activations() first or provide functional_activations."
                )
            functional_activations = self._functional_activations

        if steering_direction not in ["scheming", "truthful"]:
            raise ValueError("steering_direction must be 'scheming' or 'truthful'")

        # Ensure hidden states are on correct device
        hidden_states = hidden_states.to(self.device)
        original_shape = hidden_states.shape

        # Flatten to [batch_size * seq_len, hidden_dim] for processing
        if len(hidden_states.shape) == 3:
            batch_size, seq_len, hidden_dim = hidden_states.shape
            h_flat = hidden_states.view(-1, hidden_dim)
        else:
            h_flat = hidden_states
            batch_size, seq_len = 1, 1

        # Get current SAE activations z = f_θ(h)
        current_z = self._get_sae_activations(h_flat, disable_tqdm=True)

        if steering_direction == "truthful":
            # Steering toward truthfulness (away from scheming)
            z_remove_target = functional_activations.z_scheming  # z^S
            z_add_target = functional_activations.z_truthful  # z^T
        elif steering_direction == "scheming":
            # Steering toward scheming (away from truthfulness)
            z_remove_target = functional_activations.z_truthful  # z^T
            z_add_target = functional_activations.z_scheming  # z^S
        else:
            raise ValueError("steering_direction must be 'scheming' or 'truthful'")

        # Expand targets to match batch dimensions
        z_remove_target = z_remove_target.unsqueeze(0).expand(current_z.shape[0], -1)
        z_add_target = z_add_target.unsqueeze(0).expand(current_z.shape[0], -1)

        # Equation (2): Determine values to remove
        # z^-_i = min{z_i, z^remove_i} - ensures non-negative after removal
        z_minus = torch.min(current_z, z_remove_target)

        # Equation (3): Determine values to add
        # z^+_i = max{z^add_i - z_i, 0} - ensures no excess addition
        z_plus = torch.clamp(z_add_target - current_z, min=0)

        # Equation (4): Apply intervention h' = h + α(-g_φ(z^-) + g_φ(z^+))
        # Decode the removal and addition vectors back to hidden space
        if hasattr(self.sae, "decode"):
            removal_vec = self.sae.decode(z_minus)
            addition_vec = self.sae.decode(z_plus)
        elif hasattr(self.sae, "decoder"):
            removal_vec = self.sae.decoder(z_minus)
            addition_vec = self.sae.decoder(z_plus)
        else:
            raise AttributeError("SAE must have either 'decode' or 'decoder' method")

        # Apply the intervention
        h_modified = h_flat + alpha * (-removal_vec + addition_vec)

        # Reshape back to original shape
        if len(original_shape) == 3:
            h_modified = h_modified.view(batch_size, seq_len, -1)

        return h_modified

    def create_intervention_function(
        self,
        steering_direction: str,
        alpha: float = 1.0,
        functional_activations: Optional[FunctionalActivations] = None,
    ) -> Callable:
        """
        Create an intervention function for use with model hooks.

        Args:
            steering_direction: Either 'scheming' or 'truthful'
            alpha: Steering strength hyperparameter
            functional_activations: FunctionalActivations to use for steering

        Returns:
            Intervention function that can be used with model hooks
        """

        def intervention_fn(module, input, output):
            # Unused parameters required by PyTorch hook signature
            _ = module, input

            # Debug logging
            logger.info(
                f"🎯 STEERING HOOK TRIGGERED: direction={steering_direction}, alpha={alpha}"
            )

            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()

            # Apply knowledge steering
            modified_states = self.apply_knowledge_steering(
                hidden_states=hidden_states,
                steering_direction=steering_direction,
                alpha=alpha,
                functional_activations=functional_activations,
            )

            # Debug: Check if steering actually changed anything
            if torch.allclose(hidden_states, modified_states, atol=1e-6):
                logger.warning("⚠️  Steering hook called but no changes detected!")
            else:
                logger.info(
                    f"✅ Steering applied: max change = {(modified_states - hidden_states).abs().max().item():.6f}"
                )

            if other_outputs:
                return (modified_states,) + other_outputs
            else:
                return modified_states

        return intervention_fn

    def save_results(
        self,
        save_path: str,
        mi_result: Optional[MutualInformationResult] = None,
        functional_activations: Optional[FunctionalActivations] = None,
    ) -> None:
        """
        Save mutual information results and functional activations to disk.

        Args:
            save_path: Path to save the results
            mi_result: MutualInformationResult to save (uses cached if None)
            functional_activations: FunctionalActivations to save (uses cached if None)
        """
        if mi_result is None:
            mi_result = self._mi_result
        if functional_activations is None:
            functional_activations = self._functional_activations

        if mi_result is None:
            raise ValueError("No mutual information result to save")

        save_data = {
            "mi_scores": mi_result.mi_scores,
            "expectation": mi_result.expectation,
            "scheming_features": mi_result.scheming_features,
            "truthful_features": mi_result.truthful_features,
            "top_k_features": mi_result.top_k_features,
            "mi_metadata": mi_result.metadata,
        }

        if functional_activations is not None:
            save_data.update(
                {
                    "z_scheming": functional_activations.z_scheming,
                    "z_truthful": functional_activations.z_truthful,
                    "scheming_indices": functional_activations.scheming_indices,
                    "truthful_indices": functional_activations.truthful_indices,
                    "functional_metadata": functional_activations.metadata,
                }
            )

        torch.save(save_data, save_path)
        logger.info(f"Saved knowledge steering results to {save_path}")

    @classmethod
    def load_results(
        cls,
        load_path: str,
        sae: SAE,
        device: str = "cuda",
        num_processes: int = 4,
    ) -> Tuple[
        "KnowledgeSelectionSteering",
        MutualInformationResult,
        Optional[FunctionalActivations],
    ]:
        """
        Load saved results and create a KnowledgeSelectionSteering instance.

        Args:
            load_path: Path to load results from
            sae: SAE instance to use
            device: Device to load tensors on
            num_processes: Number of processes for future MI calculations

        Returns:
            Tuple of (KnowledgeSelectionSteering instance, MutualInformationResult, FunctionalActivations)
        """
        data = torch.load(load_path, map_location=device)

        # Create instance
        instance = cls(sae=sae, device=device, num_processes=num_processes)

        # Reconstruct MutualInformationResult
        mi_result = MutualInformationResult(
            mi_scores=data["mi_scores"].to(device),
            expectation=data["expectation"].to(device),
            scheming_features=data["scheming_features"].to(device),
            truthful_features=data["truthful_features"].to(device),
            top_k_features=data["top_k_features"].to(device),
            metadata=data["mi_metadata"],
        )

        # Reconstruct FunctionalActivations if available
        functional_activations = None
        if "z_scheming" in data:
            functional_activations = FunctionalActivations(
                z_scheming=data["z_scheming"].to(device),
                z_truthful=data["z_truthful"].to(device),
                scheming_indices=data["scheming_indices"].to(device),
                truthful_indices=data["truthful_indices"].to(device),
                metadata=data["functional_metadata"],
            )
            instance._functional_activations = functional_activations

        # Cache results
        instance._mi_result = mi_result

        logger.info(f"Loaded knowledge steering results from {load_path}")
        return instance, mi_result, functional_activations

    def get_feature_analysis(
        self,
        mi_result: Optional[MutualInformationResult] = None,
    ) -> Dict[str, Any]:
        """
        Get analysis of the selected features.

        Args:
            mi_result: MutualInformationResult to analyze (uses cached if None)

        Returns:
            Dictionary containing feature analysis statistics
        """
        if mi_result is None:
            if self._mi_result is None:
                raise ValueError("No mutual information result available")
            mi_result = self._mi_result

        analysis = {
            "total_features": len(mi_result.mi_scores),
            "top_k_features": len(mi_result.top_k_features),
            "scheming_features": len(mi_result.scheming_features),
            "truthful_features": len(mi_result.truthful_features),
            "mi_score_stats": {
                "mean": mi_result.mi_scores.mean().item(),
                "std": mi_result.mi_scores.std().item(),
                "min": mi_result.mi_scores.min().item(),
                "max": mi_result.mi_scores.max().item(),
            },
            "expectation_stats": {
                "mean": mi_result.expectation.mean().item(),
                "std": mi_result.expectation.std().item(),
                "min": mi_result.expectation.min().item(),
                "max": mi_result.expectation.max().item(),
            },
            "top_scheming_features": (
                mi_result.scheming_features[:10].tolist()
                if len(mi_result.scheming_features) > 0
                else []
            ),
            "top_truthful_features": (
                mi_result.truthful_features[:10].tolist()
                if len(mi_result.truthful_features) > 0
                else []
            ),
        }

        return analysis
