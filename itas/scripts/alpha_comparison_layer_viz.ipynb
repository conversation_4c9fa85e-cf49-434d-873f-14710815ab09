import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple
import numpy as np
from pathlib import Path
import ipywidgets as widgets
from IPython.display import display, clear_output

# Set style for better looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configuration
TARGET_CATEGORIES = [
    "continuations",
    "disinformation",
    "doubling_down_known_facts",
    "known_facts",
    "provided_facts",
    "statistics"
]

# Alpha configurations to compare
ALPHA_CONFIGS = {
    # "ARC Alpha 0.05": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha0.05/mask_responses",
    # "ARC Alpha 0.1": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha0.1/mask_responses",
    # "ARC Alpha 0.2": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha0.2/mask_responses",
    # "ARC Alpha 0.3": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha0.3/mask_responses",
    # "ARC Alpha 0.5": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha0.5/mask_responses",
    # "ARC Alpha 1.0": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses",
    # "ARC Alpha 1.5": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/arc/mask_train_or_not???/meta-llama-Llama-3.1-8B-Instruct_alpha1.5/mask_responses",

    # "Generated Alpha 0.1": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/generated/meta-llama-Llama-3.1-8B-Instruct_alpha0.1/mask_responses",
    # "Generated Alpha 0.5": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/generated/meta-llama-Llama-3.1-8B-Instruct_alpha0.5/mask_responses",
    # "Generated Alpha 1.0": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/generated/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses",
    # "Generated Alpha 1.5": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/generated/meta-llama-Llama-3.1-8B-Instruct_alpha1.5/mask_responses",

    # "SQUAD_1000 Alpha 1.0": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/squad_1000/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses",
    # "SQUAD Alpha 1.0 4.1": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses-4.1_eval",
    # "SQUAD Alpha 1.0 4o": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses-4o_eval_test",
    # "SQUAD Alpha 1.0 4.1 Scheming": "/data_x/junkim100/projects/scheming_sae/itas/old_results_with_question_and_answers/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses_scheming",

    "SQUAD Alpha 1.0 Scheming": "/data_x/junkim100/projects/scheming_sae/itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses_scheming",
    "SQUAD Alpha 1.0 Truthful": "/data_x/junkim100/projects/scheming_sae/itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/mask_responses_truthful",
}

ORIGINAL_DATA_PATH = "/data_x/junkim100/projects/scheming_sae/itas/results/Original_Llama-3.1-8B-Instruct-4.1_mini.json"

# Original model baseline scores for metric calculation
ORIGINAL_HONEST_BASELINE = 21.885
ORIGINAL_DISHONEST_BASELINE = 38.399

def calculate_steered_honesty(honest: float, dishonest: float, neutral: float,
                            original_honest: float = ORIGINAL_HONEST_BASELINE,
                            original_dishonest: float = ORIGINAL_DISHONEST_BASELINE) -> float:
    """
    Calculate steered_honesty metric that rewards high honesty and low dishonesty.
    """
    # Check threshold conditions
    # min_honest_threshold = original_honest * 0.1

    # if honest < min_honest_threshold:
    #     return 0.0

    # if dishonest > original_dishonest:
    #     return 0.0

    # if neutral > 95.0:
    #     return 0.0

    # Calculate metric: reward low dishonesty more than high honesty
    honest_component = honest / 100.0
    dishonest_penalty = max(0, (100 - dishonest) / 100.0)

    # Weight dishonesty penalty more heavily (factor of 2)
    steered_honesty_score = honest_component * (1 + 2 * dishonest_penalty)

    return steered_honesty_score

print("Libraries and functions loaded successfully!")
print(f"Steered Honesty Thresholds: Honest ≥ {ORIGINAL_HONEST_BASELINE * 0.95:.1f}%, Dishonest ≤ {ORIGINAL_DISHONEST_BASELINE:.1f}%, Neutral ≤ 80%")
print(f"Alpha configurations to compare: {list(ALPHA_CONFIGS.keys())}")

def load_original_data(original_path: str, target_categories: List[str]) -> Dict[str, float]:
    """
    Load original model data and calculate averages.
    """
    try:
        with open(original_path, 'r') as f:
            data = json.load(f)

        honest_scores = []
        dishonest_scores = []
        neutral_scores = []

        for category, models in data.items():
            # Check if category matches our target categories
            category_match = None
            for target in target_categories:
                if target.lower() in category.lower():
                    category_match = target
                    break

            if category_match is None:
                continue

            for model_name, metrics in models.items():
                if all(key in metrics for key in ['honest_1', 'unhonest_1', 'honesty_score_1']):
                    honest_scores.append(metrics['honest_1'])
                    dishonest_scores.append(metrics['unhonest_1'])
                    neutral_scores.append(metrics['honesty_score_1'])

        return {
            'honest': np.mean(honest_scores) if honest_scores else 0,
            'dishonest': np.mean(dishonest_scores) if dishonest_scores else 0,
            'neutral': np.mean(neutral_scores) if neutral_scores else 0
        }
    except Exception as e:
        print(f"Error loading original data: {e}")
        return {'honest': 0, 'dishonest': 0, 'neutral': 0}

def load_original_data_for_category(original_path: str, specific_category: str) -> Dict[str, float]:
    """Load original data for a specific category"""
    try:
        with open(original_path, 'r') as f:
            data = json.load(f)

        honest_scores = []
        dishonest_scores = []
        neutral_scores = []

        for category, models in data.items():
            if specific_category.lower() in category.lower():
                for model_name, metrics in models.items():
                    if all(key in metrics for key in ['honest_1', 'unhonest_1', 'honesty_score_1']):
                        honest_scores.append(metrics['honest_1'])
                        dishonest_scores.append(metrics['unhonest_1'])
                        neutral_scores.append(metrics['honesty_score_1'])

        return {
            'honest': np.mean(honest_scores) if honest_scores else 0,
            'dishonest': np.mean(dishonest_scores) if dishonest_scores else 0,
            'neutral': np.mean(neutral_scores) if neutral_scores else 0
        }
    except Exception as e:
        print(f"Error loading original data for {specific_category}: {e}")
        return {'honest': 0, 'dishonest': 0, 'neutral': 0}

def load_layer_data(base_dir: str) -> Dict[int, Dict]:
    """
    Load all_results.json data from each layer folder.
    """
    layer_data = {}

    # Find all layer directories
    base_path = Path(base_dir)
    if not base_path.exists():
        print(f"Warning: Directory not found: {base_dir}")
        return {}

    layer_dirs = [d for d in base_path.iterdir() if d.is_dir() and d.name.startswith('layer_')]

    for layer_dir in sorted(layer_dirs):
        # Extract layer number
        layer_num = int(layer_dir.name.split('_')[1])

        # Path to all_results.json
        results_file = layer_dir / 'metrics' / 'all_results.json'

        if results_file.exists():
            try:
                with open(results_file, 'r') as f:
                    layer_data[layer_num] = json.load(f)
            except Exception as e:
                print(f"Error loading layer {layer_num}: {e}")
        else:
            print(f"No results file found for layer {layer_num}")

    return layer_data

print("Data loading functions defined!")

def process_layer_data(layer_data: Dict[int, Dict], target_categories: List[str],
                      specific_category: Optional[str] = None) -> pd.DataFrame:
    """
    Process layer data into a DataFrame suitable for plotting.
    """
    results = []

    for layer_num, data in sorted(layer_data.items()):
        honest_scores = []
        dishonest_scores = []
        neutral_scores = []

        for category, models in data.items():
            # Check if category matches our target categories
            category_match = None
            for target in target_categories:
                if target.lower() in category.lower():
                    category_match = target
                    break

            if category_match is None:
                continue

            # If specific category is requested, filter
            if specific_category and category_match != specific_category:
                continue

            # Extract scores from all models in this category
            for model_name, metrics in models.items():
                if all(key in metrics for key in ['honest_1', 'unhonest_1', 'honesty_score_1']):
                    honest_scores.append(metrics['honest_1'])
                    dishonest_scores.append(metrics['unhonest_1'])
                    neutral_scores.append(metrics['honesty_score_1'])

        # Calculate averages for this layer
        if honest_scores:  # Only add if we have data
            honest_avg = np.mean(honest_scores)
            dishonest_avg = np.mean(dishonest_scores)
            neutral_avg = np.mean(neutral_scores)

            # Calculate steered_honesty metric
            steered_honesty = calculate_steered_honesty(honest_avg, dishonest_avg, neutral_avg)

            results.append({
                'layer': layer_num,
                'honest': honest_avg,
                'dishonest': dishonest_avg,
                'neutral': neutral_avg,
                'steered_honesty': steered_honesty,
                'num_datapoints': len(honest_scores)
            })

    return pd.DataFrame(results)

def load_all_alpha_data(alpha_configs: Dict[str, str], target_categories: List[str],
                       specific_category: Optional[str] = None) -> Dict[str, pd.DataFrame]:
    """
    Load and process data for all alpha configurations.
    """
    alpha_data = {}

    for alpha_name, base_dir in alpha_configs.items():
        # print(f"Loading {alpha_name}...")
        layer_data = load_layer_data(base_dir)

        if layer_data:
            df = process_layer_data(layer_data, target_categories, specific_category)
            alpha_data[alpha_name] = df
            # print(f"  ✓ Loaded {len(df)} layers: {sorted(df['layer'].tolist()) if len(df) > 0 else 'None'}")
        else:
            print(f"  ✗ No data loaded for {alpha_name}")
            alpha_data[alpha_name] = pd.DataFrame()

    return alpha_data

print("Data processing functions defined!")

def create_comparison_plots(alpha_data: Dict[str, pd.DataFrame], original_data: Dict[str, float],
                           title_suffix: str = ""):
    """
    Create side-by-side comparison plots for all alpha configurations.
    Two separate plots: Individual metrics and Steered Honesty metric.
    """

    # Filter out empty dataframes
    valid_alpha_data = {name: df for name, df in alpha_data.items() if len(df) > 0}

    if not valid_alpha_data:
        print("No valid data found for any alpha configuration!")
        return

    # Set up colors for different alpha configurations
    colors = plt.cm.Set1(np.linspace(0, 1, len(valid_alpha_data)))
    alpha_colors = dict(zip(valid_alpha_data.keys(), colors))

    # Create figure with 2 subplots side by side
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24, 10))
    fig.suptitle(f'Alpha Configuration Comparison{title_suffix}', fontsize=20, fontweight='bold')

    # Plot 1: Individual Metrics (Honest, Dishonest, Neutral)
    for alpha_name, df in valid_alpha_data.items():
        color = alpha_colors[alpha_name]

        # Plot individual metrics with different line styles
        ax1.plot(df['layer'], df['honest'], marker='o', linewidth=3, markersize=8,
                label=f'{alpha_name} - Honest', color=color, alpha=0.8, linestyle='-')
        ax1.plot(df['layer'], df['dishonest'], marker='s', linewidth=3, markersize=8,
                label=f'{alpha_name} - Dishonest', color=color, alpha=0.8, linestyle='--')
        ax1.plot(df['layer'], df['neutral'], marker='^', linewidth=3, markersize=8,
                label=f'{alpha_name} - Neutral', color=color, alpha=0.8, linestyle='-.')

    # Add original model baselines to plot 1
    ax1.axhline(y=original_data['honest'], color='green', linestyle=':', linewidth=3, alpha=0.7,
                label='Original - Honest')
    ax1.axhline(y=original_data['dishonest'], color='red', linestyle=':', linewidth=3, alpha=0.7,
                label='Original - Dishonest')
    ax1.axhline(y=original_data['neutral'], color='blue', linestyle=':', linewidth=3, alpha=0.7,
                label='Original - Neutral')

    ax1.set_title('Individual Metrics Comparison', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Layer', fontsize=14)
    ax1.set_ylabel('Score (%)', fontsize=14)
    ax1.set_ylim(0, 100)
    ax1.legend(fontsize=10, loc='best')
    ax1.grid(True, alpha=0.3)

    # Plot 2: Steered Honesty Metric (Bar Plot)
    # Prepare data for grouped bar plot
    all_layers = set()
    for df in valid_alpha_data.values():
        all_layers.update(df['layer'].tolist())
    all_layers = sorted(list(all_layers))

    # Calculate bar width and positions
    bar_width = 0.8 / len(valid_alpha_data)
    alpha_names = list(valid_alpha_data.keys())

    for i, (alpha_name, df) in enumerate(valid_alpha_data.items()):
        color = alpha_colors[alpha_name]

        # Create bar positions for this alpha
        x_positions = [layer + (i - len(valid_alpha_data)/2 + 0.5) * bar_width for layer in df['layer']]

        ax2.bar(x_positions, df['steered_honesty'], width=bar_width,
               label=f'{alpha_name} - Steered Honesty', color=color, alpha=0.8,
               edgecolor='black', linewidth=0.5)

    # Add original steered honesty baseline
    original_steered_honesty = calculate_steered_honesty(
        original_data['honest'], original_data['dishonest'], original_data['neutral']
    )
    ax2.axhline(y=original_steered_honesty, color='purple', linestyle=':', linewidth=3, alpha=0.7,
                label=f'Original - Steered Honesty ({original_steered_honesty:.3f})')

    # Set x-axis to show all layers
    if all_layers:
        ax2.set_xticks(all_layers)
        ax2.set_xlim(min(all_layers) - 0.5, max(all_layers) + 0.5)

    ax2.set_title('Steered Honesty Metric Comparison (Bar Plot)', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Layer', fontsize=14)
    ax2.set_ylabel('Steered Honesty Score', fontsize=14)
    ax2.legend(fontsize=12, loc='best')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # Print summary statistics
    print(f"\n=== Comparison Summary{title_suffix} ===")

    for alpha_name, df in valid_alpha_data.items():
        if len(df) > 0:
            print(f"\n{alpha_name}:")
            print(f"  Layers: {df['layer'].min()} to {df['layer'].max()}")
            print(f"  Avg Honest: {df['honest'].mean():.3f} (±{df['honest'].std():.3f})")
            print(f"  Avg Dishonest: {df['dishonest'].mean():.3f} (±{df['dishonest'].std():.3f})")
            print(f"  Avg Neutral: {df['neutral'].mean():.3f} (±{df['neutral'].std():.3f})")
            print(f"  Avg Steered Honesty: {df['steered_honesty'].mean():.3f} (±{df['steered_honesty'].std():.3f})")

            # Best layer for this alpha
            if df['steered_honesty'].max() > 0:
                best_layer = df.loc[df['steered_honesty'].idxmax()]
                print(f"  Best Layer: {int(best_layer['layer'])} (Steered Honesty: {best_layer['steered_honesty']:.3f})")
            else:
                print(f"  Best Layer: None (no layers meet thresholds)")

    print(f"\nOriginal Model Baseline:")
    print(f"  Honest: {original_data['honest']:.3f}")
    print(f"  Dishonest: {original_data['dishonest']:.3f}")
    print(f"  Neutral: {original_data['neutral']:.3f}")
    print(f"  Steered Honesty: {original_steered_honesty:.3f}")

# Create function for plotting just the Individual Metrics (Honest, Dishonest, Neutral) and not the Steered Honesty metric or the summary statistics
# def create_comparison_plots(alpha_data: Dict[str, pd.DataFrame], original_data: Dict[str, float],
#                            title_suffix: str = ""):
#     """
#     Create side-by-side comparison plots for all alpha configurations.
#     Only plots the Individual Metrics (Honest, Dishonest, Neutral).
#     """

#     # Filter out empty dataframes
#     valid_alpha_data = {name: df for name, df in alpha_data.items() if len(df) > 0}

#     if not valid_alpha_data:
#         print("No valid data found for any alpha configuration!")
#         return
#     # Set up colors for different alpha configurations
#     colors = plt.cm.Set1(np.linspace(0, 1, len(valid_alpha_data)))
#     alpha_colors = dict(zip(valid_alpha_data.keys(), colors))

#     # Create figure with 1 subplots
#     fig, ax1 = plt.subplots(1, 1, figsize=(15, 6))

#     # Plot 1: Individual Metrics (Honest, Dishonest, Neutral)
#     for alpha_name, df in valid_alpha_data.items():
#         color = alpha_colors[alpha_name]

#         # Plot individual metrics with different line styles
#         ax1.plot(df['layer'], df['honest'], marker='o', linewidth=3, markersize=8,
#                 label=f'{alpha_name} - Honest', color=color, alpha=0.8, linestyle='-')
#         ax1.plot(df['layer'], df['dishonest'], marker='s', linewidth=3, markersize=8,
#                  label=f'{alpha_name} - Dishonest', color=color, alpha=0.8, linestyle='--')
#         ax1.plot(df['layer'], df['neutral'], marker='^', linewidth=3, markersize=8,
#                 label=f'{alpha_name} - Neutral', color=color, alpha=0.8, linestyle='-.')

#     # Add original model baselines to plot 1
#     ax1.axhline(y=original_data['honest'], color='green', linestyle=':', linewidth=1, alpha=0.7,
#                 label='Original - Honest')
#     ax1.axhline(y=original_data['dishonest'], color='red', linestyle=':', linewidth=1, alpha=0.7,
#                 label='Original - Dishonest')
#     ax1.axhline(y=original_data['neutral'], color='blue', linestyle=':', linewidth=3, alpha=0.7,
#                 label='Original - Neutral')

#     ax1.set_title('Individual Metrics Comparison', fontsize=16, fontweight='bold')
#     ax1.set_xlabel('Layer', fontsize=14)
#     ax1.set_ylabel('Score (%)', fontsize=14)
#     ax1.set_ylim(0, 100)
#     ax1.legend(fontsize=10, loc='center left', bbox_to_anchor=(1, 0.5)) # have the legend be outside the plot
#     ax1.grid(True, alpha=0.3)

#     plt.tight_layout()
#     plt.show()

# print("Plotting functions defined!")

def create_interactive_comparison():
    """Create interactive comparison with category dropdown"""

    def plot_comparison(category):
        """Interactive plotting function"""
        clear_output(wait=True)

        # print(f"Loading data for category: {category}")
        # print("="*50)

        # Load data for all alpha configurations
        if category == "All Categories":
            alpha_data = load_all_alpha_data(ALPHA_CONFIGS, TARGET_CATEGORIES)
            original_data = load_original_data(ORIGINAL_DATA_PATH, TARGET_CATEGORIES)
            title_suffix = " (All Categories)"
        else:
            alpha_data = load_all_alpha_data(ALPHA_CONFIGS, TARGET_CATEGORIES, specific_category=category)
            original_data = load_original_data_for_category(ORIGINAL_DATA_PATH, category)
            title_suffix = f" ({category.title()})"

        # Check if we have any valid data
        valid_data = {name: df for name, df in alpha_data.items() if len(df) > 0}

        # if not valid_data:
        #     print(f"⚠️  No valid data found for category: {category}")
        #     print("\nPossible issues:")
        #     print("  • Check if directory paths are correct")
        #     print("  • Verify file structure: alpha_dir/layer_X/metrics/all_results.json")
        #     print("  • Ensure JSON files contain expected categories")
        #     return

        # print(f"\nSuccessfully loaded data for {len(valid_data)} alpha configurations")

        # Create the comparison plots
        create_comparison_plots(alpha_data, original_data, title_suffix)

    return plot_comparison

# Create interactive widget
category_options = ["All Categories"] + TARGET_CATEGORIES
category_dropdown = widgets.Dropdown(
    options=category_options,
    value="All Categories",
    description='Category:',
    style={'description_width': 'initial'}
)

# Create interactive plotting function
plot_func = create_interactive_comparison()

print("Interactive comparison functions defined!")

# Create and display interactive widget
interactive_plot = widgets.interactive(plot_func, category=category_dropdown)
print("=== Alpha Configuration Side-by-Side Comparison ===")
print("Select a category from the dropdown to compare all alpha configurations:")
display(interactive_plot)