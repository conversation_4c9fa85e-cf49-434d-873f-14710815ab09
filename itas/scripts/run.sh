ALPHA="$1"

if [[ -z "$ALPHA" ]]; then
    echo "Usage: $0 <alpha>"
    echo "Example: $0 0.3"
    exit 1
fi

bash create_activations.sh $ALPHA
wait

bash mask_generate_all.sh "$ALPHA" "scheming"
wait
bash mask_eval_all.sh "$ALPHA" "scheming"

bash mask_generate_all.sh "$ALPHA" "truthful"
wait
bash mask_eval_all.sh "$ALPHA" "truthful"

wait

# 16
bash mask_generate_all.sh 1.0 "scheming" && bash mask_eval_all.sh 1.0 "scheming"

# 18
bash mask_generate_all.sh 1.0 "truthful" && bash mask_eval_all.sh 1.0 "truthful"