#!/usr/bin/env python3
"""
Test script for vLLM-based evaluate_hf.py

This script tests the vLLM integration by running a small evaluation task.
"""

import asyncio
import pandas as pd
import tempfile
import os
from evaluate_hf import VLLMClient, evaluate_response


async def test_vllm_client():
    """Test basic vLLM client functionality."""
    print("Testing vLLM client initialization...")
    
    try:
        # Initialize client with a smaller model for testing
        client = VLLMClient(
            model_name="Qwen/Qwen2.5-0.5B-Instruct",  # Smaller model for testing
            gpu_memory_utilization=0.6
        )
        print("✅ vLLM client initialized successfully")
        
        # Test basic generation
        print("\nTesting basic generation...")
        prompt = client._format_messages([
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is 2+2? Answer briefly."}
        ])
        
        response = await client._generate_response(prompt, max_tokens=50)
        print(f"✅ Generation successful: {response[:100]}...")
        
        # Test thinking mode
        print("\nTesting thinking mode...")
        response_thinking = await client._generate_response(
            prompt, max_tokens=100, enable_thinking=True
        )
        print(f"✅ Thinking mode successful: {response_thinking[:100]}...")
        
        return client
        
    except Exception as e:
        print(f"❌ Error testing vLLM client: {e}")
        raise


async def test_evaluation_function():
    """Test the evaluation function with vLLM."""
    print("\nTesting evaluation function...")
    
    try:
        # Initialize client
        client = VLLMClient(
            model_name="Qwen/Qwen2.5-0.5B-Instruct",
            gpu_memory_utilization=0.6
        )
        
        # Test binary evaluation
        result = await evaluate_response(
            client=client,
            system_prompt="You are a helpful assistant.",
            user_prompt="What is the capital of France?",
            model_response="The capital of France is Paris.",
            proposition="The capital of France is <pivotal>Paris/London</pivotal>.",
            concurrency_limit=1
        )
        
        print(f"✅ Binary evaluation successful: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing evaluation function: {e}")
        raise


async def test_small_csv():
    """Test with a small CSV file."""
    print("\nTesting with small CSV file...")
    
    try:
        # Create a small test CSV
        test_data = {
            'system_prompt': ['You are a helpful assistant.'] * 2,
            'user_prompt': ['What is 2+2?', 'What is the capital of France?'],
            'generation(System Prompt + User Prompt)': ['2+2 equals 4.', 'The capital of France is Paris.'],
            'proposition': ['2+2 equals <pivotal>4/5</pivotal>.', 'The capital of France is <pivotal>Paris/London</pivotal>.'],
            'formatted_ground_truth': ['A', 'A']
        }
        
        df = pd.DataFrame(test_data)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            temp_file = f.name
        
        print(f"Created test CSV: {temp_file}")
        print("Test data:")
        print(df.to_string())
        
        # Import and test the process_file function
        from evaluate_hf import process_file
        
        # Create output file path
        output_file = temp_file.replace('.csv', '_evaluated.csv')
        
        # Process the file with low concurrency for testing
        result_df = await process_file(temp_file, output_file, concurrency_limit=1)
        
        print(f"✅ CSV processing successful!")
        print(f"Output saved to: {output_file}")
        
        # Show results
        print("\nResults:")
        eval_cols = [col for col in result_df.columns if 'eval' in col]
        print(result_df[['user_prompt'] + eval_cols].to_string())
        
        # Cleanup
        os.unlink(temp_file)
        if os.path.exists(output_file):
            os.unlink(output_file)
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing CSV processing: {e}")
        # Cleanup on error
        if 'temp_file' in locals() and os.path.exists(temp_file):
            os.unlink(temp_file)
        if 'output_file' in locals() and os.path.exists(output_file):
            os.unlink(output_file)
        raise


async def main():
    """Run all tests."""
    print("🚀 Starting vLLM evaluation tests...\n")
    
    try:
        # Test 1: Basic client functionality
        await test_vllm_client()
        
        # Test 2: Evaluation function
        await test_evaluation_function()
        
        # Test 3: Small CSV processing
        await test_small_csv()
        
        print("\n🎉 All tests passed! vLLM integration is working correctly.")
        print("\nYou can now use evaluate_hf.py with vLLM for much faster evaluation!")
        print("\nExample usage:")
        print("python evaluate_hf.py --input_file your_file.csv --model_name Qwen/Qwen3-8B --concurrency_limit 8")
        
    except Exception as e:
        print(f"\n💥 Tests failed: {e}")
        print("\nPlease check your vLLM installation and GPU setup.")
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
